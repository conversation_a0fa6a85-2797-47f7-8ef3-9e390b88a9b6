/* 科技感粒子波浪加载器 */
#loader-wrapper.tech-style {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  overflow: hidden;
}

/* 动态背景粒子 */
#loader-wrapper.tech-style::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.3) 0%, transparent 50%);
  animation: particleFloat 8s ease-in-out infinite;
}

@keyframes particleFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.3; }
  50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
}

/* 主加载容器 */
.tech-loader-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
}

/* 波浪加载器 */
.wave-loader {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  box-shadow: 
    0 0 50px rgba(102, 126, 234, 0.5),
    inset 0 0 50px rgba(255, 255, 255, 0.1);
}

.wave-loader::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 50%;
  transform: translate(-50%, -50%) rotate(0deg);
  animation: waveRotate 3s linear infinite;
}

@keyframes waveRotate {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 中心文字 */
.wave-loader .center-text {
  position: relative;
  z-index: 5;
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
  animation: textPulse 2s ease-in-out infinite;
}

@keyframes textPulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

/* 环形进度条 */
.circular-progress {
  position: absolute;
  width: 220px;
  height: 220px;
  border-radius: 50%;
  background: conic-gradient(from 0deg, #667eea 0%, #764ba2 var(--progress, 0%), transparent var(--progress, 0%));
  animation: progressSpin 4s ease-in-out infinite;
}

@keyframes progressSpin {
  0% { --progress: 0%; transform: rotate(0deg); }
  100% { --progress: 100%; transform: rotate(360deg); }
}

/* 浮动粒子 */
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #667eea, transparent);
  border-radius: 50%;
  animation: floatUp 6s linear infinite;
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { left: 20%; animation-delay: 1s; }
.particle:nth-child(3) { left: 30%; animation-delay: 2s; }
.particle:nth-child(4) { left: 40%; animation-delay: 3s; }
.particle:nth-child(5) { left: 50%; animation-delay: 4s; }
.particle:nth-child(6) { left: 60%; animation-delay: 5s; }
.particle:nth-child(7) { left: 70%; animation-delay: 0.5s; }
.particle:nth-child(8) { left: 80%; animation-delay: 1.5s; }
.particle:nth-child(9) { left: 90%; animation-delay: 2.5s; }

@keyframes floatUp {
  0% {
    bottom: -10px;
    opacity: 0;
    transform: translateX(0) scale(0);
  }
  10% {
    opacity: 1;
    transform: translateX(10px) scale(1);
  }
  90% {
    opacity: 1;
    transform: translateX(-10px) scale(1);
  }
  100% {
    bottom: 100vh;
    opacity: 0;
    transform: translateX(0) scale(0);
  }
}

/* 加载文字 */
.tech-loading-text {
  margin-top: 40px;
  font-size: 18px;
  color: #ffffff;
  font-weight: 300;
  letter-spacing: 3px;
  text-transform: uppercase;
  animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes textGlow {
  0% {
    text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
    opacity: 0.8;
  }
  100% {
    text-shadow: 0 0 20px rgba(102, 126, 234, 1), 0 0 30px rgba(118, 75, 162, 0.8);
    opacity: 1;
  }
}

/* 退出动画 */
#loader-wrapper.tech-style.fade-out {
  animation: techFadeOut 0.8s ease-in-out forwards;
}

@keyframes techFadeOut {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(1.1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wave-loader {
    width: 150px;
    height: 150px;
  }
  
  .circular-progress {
    width: 170px;
    height: 170px;
  }
  
  .wave-loader .center-text {
    font-size: 18px;
  }
  
  .tech-loading-text {
    font-size: 14px;
    letter-spacing: 2px;
  }
}
