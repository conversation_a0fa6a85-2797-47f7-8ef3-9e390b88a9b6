/**
 * 加载效果配置文件
 * 可以根据需要自定义加载效果的各种参数
 */

export const loadingConfig = {
  // 默认加载样式：tech, geometric, liquid
  defaultStyle: 'tech',
  
  // 最小显示时间（毫秒）
  minDisplayTime: 1500,
  
  // 最大显示时间（毫秒）
  maxDisplayTime: 8000,
  
  // 是否启用进度模拟
  enableProgressSimulation: true,
  
  // 进度更新间隔（毫秒）
  progressUpdateInterval: 200,
  
  // 退出动画持续时间（毫秒）
  fadeOutDuration: 1000,
  
  // 品牌文字配置
  brandText: {
    primary: 'BladeX',
    secondary: '微服务平台',
    loading: '系统加载中'
  },
  
  // 颜色主题配置
  themes: {
    tech: {
      primary: '#667eea',
      secondary: '#764ba2',
      accent: '#4facfe',
      background: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%)'
    },
    geometric: {
      primary: '#667eea',
      secondary: '#764ba2',
      accent: '#ffffff',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    liquid: {
      primary: '#1e3c72',
      secondary: '#2a5298',
      accent: '#4facfe',
      background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
    }
  },
  
  // 动画配置
  animations: {
    // 科技风格动画配置
    tech: {
      particleCount: 9,
      waveSpeed: 3, // 秒
      pulseSpeed: 2, // 秒
      rotationSpeed: 4 // 秒
    },
    
    // 几何风格动画配置
    geometric: {
      ringSpeed: 2, // 秒
      morphSpeed: 4, // 秒
      floatSpeed: 8, // 秒
      particleCount: 5
    },
    
    // 液体风格动画配置
    liquid: {
      waveSpeed: 4, // 秒
      bubbleCount: 4,
      pulseSpeed: 3, // 秒
      ringExpandSpeed: 3 // 秒
    }
  },
  
  // 响应式断点
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1200
  },
  
  // 性能配置
  performance: {
    // 是否使用 GPU 加速
    useGPUAcceleration: true,
    
    // 是否在低性能设备上简化动画
    simplifyOnLowPerformance: true,
    
    // 低性能设备检测阈值
    lowPerformanceThreshold: {
      memory: 4, // GB
      cores: 4
    }
  },
  
  // 可访问性配置
  accessibility: {
    // 是否尊重用户的减少动画偏好
    respectReducedMotion: true,
    
    // 是否提供跳过动画选项
    allowSkip: true,
    
    // 跳过动画的快捷键
    skipKey: 'Escape'
  }
};

/**
 * 获取当前设备的性能等级
 * @returns {string} 性能等级：high, medium, low
 */
export function getPerformanceLevel() {
  // 检测设备内存
  const memory = navigator.deviceMemory || 4;
  
  // 检测 CPU 核心数
  const cores = navigator.hardwareConcurrency || 4;
  
  // 检测是否为移动设备
  const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  
  if (memory >= 8 && cores >= 8 && !isMobile) {
    return 'high';
  } else if (memory >= 4 && cores >= 4) {
    return 'medium';
  } else {
    return 'low';
  }
}

/**
 * 检测用户是否偏好减少动画
 * @returns {boolean}
 */
export function prefersReducedMotion() {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * 根据性能等级调整配置
 * @param {object} config - 原始配置
 * @param {string} performanceLevel - 性能等级
 * @returns {object} 调整后的配置
 */
export function adjustConfigForPerformance(config, performanceLevel) {
  const adjustedConfig = { ...config };
  
  if (performanceLevel === 'low') {
    // 低性能设备简化配置
    adjustedConfig.animations.tech.particleCount = 5;
    adjustedConfig.animations.geometric.particleCount = 3;
    adjustedConfig.animations.liquid.bubbleCount = 2;
    
    // 减慢动画速度以降低 CPU 使用率
    Object.keys(adjustedConfig.animations).forEach(style => {
      Object.keys(adjustedConfig.animations[style]).forEach(key => {
        if (key.includes('Speed')) {
          adjustedConfig.animations[style][key] *= 1.5;
        }
      });
    });
  }
  
  return adjustedConfig;
}

/**
 * 创建自定义主题
 * @param {object} themeConfig - 主题配置
 * @returns {object} 完整的主题对象
 */
export function createCustomTheme(themeConfig) {
  const defaultTheme = loadingConfig.themes.tech;
  
  return {
    ...defaultTheme,
    ...themeConfig
  };
}

export default loadingConfig;
