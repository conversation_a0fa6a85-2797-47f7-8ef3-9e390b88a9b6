/* 几何变形加载器 */
#loader-wrapper.geometric-style {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  overflow: hidden;
}

/* 动态背景网格 */
#loader-wrapper.geometric-style::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 10s linear infinite;
  opacity: 0.3;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* 主几何容器 */
.geometric-loader-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
}

/* 几何形状组合 */
.geometric-shapes {
  position: relative;
  width: 200px;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 外圈旋转环 */
.rotating-ring {
  position: absolute;
  width: 180px;
  height: 180px;
  border: 3px solid transparent;
  border-top: 3px solid rgba(255, 255, 255, 0.8);
  border-right: 3px solid rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: ringRotate 2s linear infinite;
}

.rotating-ring:nth-child(2) {
  width: 140px;
  height: 140px;
  border-top: 3px solid rgba(255, 255, 255, 0.6);
  border-left: 3px solid rgba(255, 255, 255, 0.4);
  animation: ringRotate 3s linear infinite reverse;
}

@keyframes ringRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 中心几何图形 */
.center-geometry {
  position: relative;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: geometryMorph 4s ease-in-out infinite;
  box-shadow: 
    0 0 30px rgba(255, 255, 255, 0.3),
    inset 0 0 30px rgba(255, 255, 255, 0.1);
}

@keyframes geometryMorph {
  0%, 100% {
    border-radius: 20px;
    transform: rotate(0deg) scale(1);
  }
  25% {
    border-radius: 50%;
    transform: rotate(90deg) scale(1.1);
  }
  50% {
    border-radius: 0;
    transform: rotate(180deg) scale(0.9);
  }
  75% {
    border-radius: 50%;
    transform: rotate(270deg) scale(1.1);
  }
}

/* 内部小几何体 */
.inner-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mini-shape {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: miniShapeOrbit 3s linear infinite;
}

.mini-shape:nth-child(1) {
  animation-delay: 0s;
  transform-origin: 25px 0;
}

.mini-shape:nth-child(2) {
  animation-delay: 0.5s;
  transform-origin: 0 25px;
}

.mini-shape:nth-child(3) {
  animation-delay: 1s;
  transform-origin: -25px 0;
}

.mini-shape:nth-child(4) {
  animation-delay: 1.5s;
  transform-origin: 0 -25px;
}

@keyframes miniShapeOrbit {
  0% { transform: rotate(0deg) translateX(25px) rotate(0deg); }
  100% { transform: rotate(360deg) translateX(25px) rotate(-360deg); }
}

/* 浮动几何元素 */
.floating-geometry {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.float-shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  animation: floatGeometry 8s ease-in-out infinite;
}

.float-shape.triangle {
  width: 0;
  height: 0;
  background: transparent;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 15px solid rgba(255, 255, 255, 0.2);
}

.float-shape.square {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.float-shape.circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.float-shape:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.float-shape:nth-child(2) { top: 30%; right: 15%; animation-delay: 1s; }
.float-shape:nth-child(3) { bottom: 25%; left: 20%; animation-delay: 2s; }
.float-shape:nth-child(4) { bottom: 35%; right: 10%; animation-delay: 3s; }
.float-shape:nth-child(5) { top: 60%; left: 80%; animation-delay: 4s; }

@keyframes floatGeometry {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
    opacity: 0.8;
  }
}

/* 进度指示器 */
.geometric-progress {
  margin-top: 50px;
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.geometric-progress::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: progressSlide 2s ease-in-out infinite;
}

@keyframes progressSlide {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 加载文字 */
.geometric-loading-text {
  margin-top: 30px;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
  letter-spacing: 2px;
  text-transform: uppercase;
  animation: textFade 3s ease-in-out infinite;
}

@keyframes textFade {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 退出动画 */
#loader-wrapper.geometric-style.fade-out {
  animation: geometricFadeOut 0.6s ease-in-out forwards;
}

@keyframes geometricFadeOut {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .geometric-shapes {
    width: 150px;
    height: 150px;
  }
  
  .rotating-ring {
    width: 130px;
    height: 130px;
  }
  
  .rotating-ring:nth-child(2) {
    width: 100px;
    height: 100px;
  }
  
  .center-geometry {
    width: 60px;
    height: 60px;
  }
  
  .geometric-progress {
    width: 150px;
  }
  
  .geometric-loading-text {
    font-size: 14px;
    letter-spacing: 1px;
  }
}
