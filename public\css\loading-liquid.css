/* 品牌主题液体加载器 */
#loader-wrapper.liquid-style {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  overflow: hidden;
}

/* 动态背景波浪 */
#loader-wrapper.liquid-style::before {
  content: '';
  position: absolute;
  width: 200%;
  height: 200%;
  top: -50%;
  left: -50%;
  background: 
    radial-gradient(circle at 30% 70%, rgba(42, 82, 152, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(30, 60, 114, 0.3) 0%, transparent 50%);
  animation: liquidBackground 12s ease-in-out infinite;
}

@keyframes liquidBackground {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

/* 主液体容器 */
.liquid-loader-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
}

/* 液体球体 */
.liquid-sphere {
  position: relative;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  box-shadow: 
    0 0 50px rgba(102, 126, 234, 0.4),
    inset 0 0 50px rgba(255, 255, 255, 0.1);
  animation: spherePulse 3s ease-in-out infinite;
}

@keyframes spherePulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 液体波浪效果 */
.liquid-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 60%;
  background: linear-gradient(0deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 0 0 50% 50%;
  animation: liquidRise 4s ease-in-out infinite;
}

.liquid-wave::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 0;
  width: 100%;
  height: 40px;
  background: 
    radial-gradient(ellipse at center, transparent 20%, #4facfe 21%, #4facfe 40%, transparent 41%),
    linear-gradient(90deg, transparent 46%, #4facfe 47%, #4facfe 53%, transparent 54%);
  background-size: 40px 40px, 20px 20px;
  animation: waveMove 2s linear infinite;
}

@keyframes liquidRise {
  0% { height: 20%; }
  50% { height: 80%; }
  100% { height: 20%; }
}

@keyframes waveMove {
  0% { background-position: 0 0, 0 0; }
  100% { background-position: 40px 0, 20px 0; }
}

/* 气泡效果 */
.liquid-bubbles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.bubble {
  position: absolute;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: bubbleFloat 4s ease-in-out infinite;
}

.bubble:nth-child(1) {
  width: 10px;
  height: 10px;
  left: 20%;
  animation-delay: 0s;
}

.bubble:nth-child(2) {
  width: 15px;
  height: 15px;
  left: 40%;
  animation-delay: 1s;
}

.bubble:nth-child(3) {
  width: 8px;
  height: 8px;
  left: 60%;
  animation-delay: 2s;
}

.bubble:nth-child(4) {
  width: 12px;
  height: 12px;
  left: 80%;
  animation-delay: 3s;
}

@keyframes bubbleFloat {
  0% {
    bottom: 0;
    opacity: 0;
    transform: translateX(0) scale(0);
  }
  10% {
    opacity: 1;
    transform: translateX(10px) scale(1);
  }
  90% {
    opacity: 1;
    transform: translateX(-10px) scale(1);
  }
  100% {
    bottom: 100%;
    opacity: 0;
    transform: translateX(0) scale(0);
  }
}

/* 中心品牌文字 */
.brand-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 5;
  color: #ffffff;
  font-size: 24px;
  font-weight: bold;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
  animation: brandGlow 2s ease-in-out infinite alternate;
}

@keyframes brandGlow {
  0% {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    text-shadow: 0 0 30px rgba(255, 255, 255, 1);
    transform: translate(-50%, -50%) scale(1.05);
  }
}

/* 环形进度指示器 */
.liquid-progress-ring {
  position: absolute;
  width: 220px;
  height: 220px;
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    #4facfe var(--progress, 0deg),
    transparent var(--progress, 0deg)
  );
  animation: progressRotate 5s ease-in-out infinite;
}

@keyframes progressRotate {
  0% { --progress: 0deg; transform: rotate(0deg); }
  100% { --progress: 360deg; transform: rotate(360deg); }
}

/* 外围装饰环 */
.decorative-rings {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.deco-ring {
  position: absolute;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: ringExpand 3s ease-in-out infinite;
}

.deco-ring:nth-child(1) {
  width: 250px;
  height: 250px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 0s;
}

.deco-ring:nth-child(2) {
  width: 300px;
  height: 300px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 1s;
}

.deco-ring:nth-child(3) {
  width: 350px;
  height: 350px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 2s;
}

@keyframes ringExpand {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

/* 加载状态文字 */
.liquid-loading-text {
  margin-top: 50px;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
  letter-spacing: 3px;
  text-transform: uppercase;
  animation: textWave 3s ease-in-out infinite;
}

@keyframes textWave {
  0%, 100% {
    opacity: 0.7;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-5px);
  }
}

/* 退出动画 */
#loader-wrapper.liquid-style.fade-out {
  animation: liquidFadeOut 1s ease-in-out forwards;
}

@keyframes liquidFadeOut {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .liquid-sphere {
    width: 150px;
    height: 150px;
  }
  
  .liquid-progress-ring {
    width: 170px;
    height: 170px;
  }
  
  .brand-text {
    font-size: 18px;
  }
  
  .liquid-loading-text {
    font-size: 14px;
    letter-spacing: 2px;
  }
  
  .deco-ring:nth-child(1) { width: 200px; height: 200px; }
  .deco-ring:nth-child(2) { width: 250px; height: 250px; }
  .deco-ring:nth-child(3) { width: 300px; height: 300px; }
}
