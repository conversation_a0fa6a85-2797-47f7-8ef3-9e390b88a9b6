/**
 * 酷炫加载效果管理器
 * 支持多种加载效果切换和管理
 */
class LoadingManager {
  constructor() {
    this.loaderWrapper = null;
    this.currentStyle = 'tech'; // 默认样式：tech, geometric, liquid
    this.isVisible = true;
    this.hideTimeout = null;
    this.progressValue = 0;
    this.progressInterval = null;
    
    this.init();
  }

  init() {
    this.loaderWrapper = document.getElementById('loader-wrapper');
    if (!this.loaderWrapper) {
      console.warn('Loader wrapper not found');
      return;
    }

    // 设置默认样式
    this.setStyle(this.currentStyle);
    
    // 开始进度模拟
    this.startProgressSimulation();
    
    // 监听页面加载完成
    this.setupLoadingComplete();
  }

  /**
   * 设置加载器样式
   * @param {string} style - 样式名称：tech, geometric, liquid
   */
  setStyle(style) {
    if (!this.loaderWrapper) return;

    // 移除所有样式类
    this.loaderWrapper.classList.remove('tech-style', 'geometric-style', 'liquid-style');
    
    // 添加新样式类
    this.loaderWrapper.classList.add(`${style}-style`);
    this.currentStyle = style;

    // 更新内容
    this.updateContent(style);
  }

  /**
   * 更新加载器内容
   * @param {string} style - 样式名称
   */
  updateContent(style) {
    const contentMap = {
      tech: this.createTechContent(),
      geometric: this.createGeometricContent(),
      liquid: this.createLiquidContent()
    };

    this.loaderWrapper.innerHTML = contentMap[style] || contentMap.tech;
  }

  /**
   * 创建科技风格内容
   */
  createTechContent() {
    return `
      <div class="tech-loader-container">
        <div class="floating-particles">
          ${Array.from({length: 9}, (_, i) => `<div class="particle"></div>`).join('')}
        </div>
        <div class="circular-progress"></div>
        <div class="wave-loader">
          <div class="center-text">BladeX</div>
        </div>
        <div class="tech-loading-text">系统加载中</div>
      </div>
    `;
  }

  /**
   * 创建几何风格内容
   */
  createGeometricContent() {
    return `
      <div class="geometric-loader-container">
        <div class="floating-geometry">
          <div class="float-shape triangle"></div>
          <div class="float-shape square"></div>
          <div class="float-shape circle"></div>
          <div class="float-shape triangle"></div>
          <div class="float-shape square"></div>
        </div>
        <div class="geometric-shapes">
          <div class="rotating-ring"></div>
          <div class="rotating-ring"></div>
          <div class="center-geometry">
            <div class="inner-shapes">
              <div class="mini-shape"></div>
              <div class="mini-shape"></div>
              <div class="mini-shape"></div>
              <div class="mini-shape"></div>
            </div>
          </div>
        </div>
        <div class="geometric-progress"></div>
        <div class="geometric-loading-text">资源加载中</div>
      </div>
    `;
  }

  /**
   * 创建液体风格内容
   */
  createLiquidContent() {
    return `
      <div class="liquid-loader-container">
        <div class="decorative-rings">
          <div class="deco-ring"></div>
          <div class="deco-ring"></div>
          <div class="deco-ring"></div>
        </div>
        <div class="liquid-progress-ring"></div>
        <div class="liquid-sphere">
          <div class="liquid-wave"></div>
          <div class="liquid-bubbles">
            <div class="bubble"></div>
            <div class="bubble"></div>
            <div class="bubble"></div>
            <div class="bubble"></div>
          </div>
          <div class="brand-text">BladeX</div>
        </div>
        <div class="liquid-loading-text">微服务平台</div>
      </div>
    `;
  }

  /**
   * 开始进度模拟
   */
  startProgressSimulation() {
    this.progressValue = 0;
    this.progressInterval = setInterval(() => {
      this.progressValue += Math.random() * 15;
      if (this.progressValue >= 100) {
        this.progressValue = 100;
        clearInterval(this.progressInterval);
      }
      this.updateProgress(this.progressValue);
    }, 200);
  }

  /**
   * 更新进度
   * @param {number} progress - 进度值 (0-100)
   */
  updateProgress(progress) {
    const progressElements = document.querySelectorAll('.circular-progress, .liquid-progress-ring');
    progressElements.forEach(el => {
      el.style.setProperty('--progress', `${progress * 3.6}deg`);
    });
  }

  /**
   * 设置加载完成监听
   */
  setupLoadingComplete() {
    // 监听 Vue 应用挂载完成
    const checkAppReady = () => {
      const app = document.querySelector('#app > div:not(#loader-wrapper)');
      if (app && app.children.length > 0) {
        // 确保最小显示时间
        setTimeout(() => {
          this.hide();
        }, 1500);
        return;
      }
      setTimeout(checkAppReady, 100);
    };

    // 页面加载完成后开始检查
    if (document.readyState === 'complete') {
      checkAppReady();
    } else {
      window.addEventListener('load', checkAppReady);
    }

    // 最大显示时间限制
    setTimeout(() => {
      if (this.isVisible) {
        this.hide();
      }
    }, 8000);
  }

  /**
   * 显示加载器
   */
  show() {
    if (!this.loaderWrapper) return;
    
    this.isVisible = true;
    this.loaderWrapper.style.display = 'flex';
    this.loaderWrapper.classList.remove('fade-out');
    
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }
  }

  /**
   * 隐藏加载器
   */
  hide() {
    if (!this.loaderWrapper || !this.isVisible) return;

    this.isVisible = false;
    this.loaderWrapper.classList.add('fade-out');
    
    // 等待动画完成后隐藏
    this.hideTimeout = setTimeout(() => {
      if (this.loaderWrapper) {
        this.loaderWrapper.style.display = 'none';
      }
      if (this.progressInterval) {
        clearInterval(this.progressInterval);
      }
    }, 1000);
  }

  /**
   * 切换样式（用于测试）
   * @param {string} style - 样式名称
   */
  switchStyle(style) {
    if (['tech', 'geometric', 'liquid'].includes(style)) {
      this.setStyle(style);
    }
  }

  /**
   * 销毁加载器
   */
  destroy() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
    }
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
    }
    if (this.loaderWrapper) {
      this.loaderWrapper.remove();
    }
  }
}

// 全局实例
window.LoadingManager = LoadingManager;

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
  window.loadingManager = new LoadingManager();
});

// 提供全局方法
window.showLoader = () => window.loadingManager?.show();
window.hideLoader = () => window.loadingManager?.hide();
window.switchLoaderStyle = (style) => window.loadingManager?.switchStyle(style);
