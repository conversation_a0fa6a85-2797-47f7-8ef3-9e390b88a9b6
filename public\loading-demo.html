<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>酷炫加载效果演示 - BladeX</title>
    <link rel="stylesheet" href="/css/loading-tech.css">
    <link rel="stylesheet" href="/css/loading-geometric.css">
    <link rel="stylesheet" href="/css/loading-liquid.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }
        
        .demo-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .demo-subtitle {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        .style-selector {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }
        
        .style-btn {
            padding: 12px 24px;
            border: 2px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.1);
            color: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            backdrop-filter: blur(10px);
        }
        
        .style-btn:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.6);
            transform: translateY(-2px);
        }
        
        .style-btn.active {
            background: rgba(255,255,255,0.3);
            border-color: white;
        }
        
        .control-panel {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .control-btn {
            padding: 10px 20px;
            border: 1px solid rgba(255,255,255,0.5);
            background: rgba(255,255,255,0.1);
            color: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }
        
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .feature-card {
            background: rgba(255,255,255,0.1);
            padding: 2rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .feature-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #fff;
        }
        
        .feature-desc {
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .integration-code {
            background: rgba(0,0,0,0.3);
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 2rem;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }
        
        @media (max-width: 768px) {
            .demo-title {
                font-size: 2rem;
            }
            
            .style-selector {
                flex-direction: column;
                align-items: center;
            }
            
            .control-panel {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🚀 酷炫加载效果演示</h1>
        <p class="demo-subtitle">为 BladeX 微服务平台设计的现代化加载动画</p>
        
        <div class="style-selector">
            <button class="style-btn active" data-style="tech">
                🔬 科技感粒子波浪
            </button>
            <button class="style-btn" data-style="geometric">
                📐 几何变形动画
            </button>
            <button class="style-btn" data-style="liquid">
                💧 品牌液体效果
            </button>
        </div>
        
        <div class="control-panel">
            <button class="control-btn" onclick="showLoader()">显示加载器</button>
            <button class="control-btn" onclick="hideLoader()">隐藏加载器</button>
            <button class="control-btn" onclick="resetDemo()">重置演示</button>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <h3 class="feature-title">🎨 现代化设计</h3>
                <p class="feature-desc">
                    采用最新的 CSS3 动画技术，包括渐变、变换、滤镜等效果，
                    打造视觉冲击力强的现代化加载体验。
                </p>
            </div>
            
            <div class="feature-card">
                <h3 class="feature-title">📱 响应式适配</h3>
                <p class="feature-desc">
                    完美适配各种屏幕尺寸，从桌面端到移动端都能提供
                    一致的优秀体验，确保在不同设备上的显示效果。
                </p>
            </div>
            
            <div class="feature-card">
                <h3 class="feature-title">⚡ 性能优化</h3>
                <p class="feature-desc">
                    使用 GPU 加速的 CSS 动画，避免 JavaScript 密集计算，
                    确保流畅的动画效果同时不影响页面加载性能。
                </p>
            </div>
        </div>
        
        <div class="integration-code">
            <h3>🔧 集成方法：</h3>
            <pre><code>// 1. 引入样式文件
&lt;link rel="stylesheet" href="/css/loading-tech.css"&gt;
&lt;link rel="stylesheet" href="/css/loading-geometric.css"&gt;
&lt;link rel="stylesheet" href="/css/loading-liquid.css"&gt;

// 2. 引入管理脚本
&lt;script src="/js/loading-manager.js"&gt;&lt;/script&gt;

// 3. 使用方法
window.loadingManager.setStyle('tech');     // 设置样式
window.showLoader();                        // 显示加载器
window.hideLoader();                        // 隐藏加载器</code></pre>
        </div>
    </div>

    <!-- 加载器容器 -->
    <div id="loader-wrapper" style="display: none;">
        <!-- 内容将由 JavaScript 动态生成 -->
    </div>

    <script src="/js/loading-manager.js"></script>
    <script>
        // 样式切换功能
        document.querySelectorAll('.style-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                // 更新按钮状态
                document.querySelectorAll('.style-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                // 切换样式
                const style = btn.dataset.style;
                if (window.loadingManager) {
                    window.loadingManager.switchStyle(style);
                }
            });
        });
        
        // 重置演示
        function resetDemo() {
            if (window.loadingManager) {
                window.loadingManager.hide();
                setTimeout(() => {
                    window.loadingManager.show();
                }, 500);
            }
        }
        
        // 页面加载完成后自动显示演示
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (window.loadingManager) {
                    window.loadingManager.show();
                    // 3秒后自动隐藏
                    setTimeout(() => {
                        window.loadingManager.hide();
                    }, 3000);
                }
            }, 1000);
        });
    </script>
</body>
</html>
